import 'package:equatable/equatable.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/models/common_model/common_model.dart';
import 'package:room_eight/models/home_model/block_profile_model.dart';
import 'package:room_eight/models/home_model/get_all_profile_model.dart';
import 'package:room_eight/models/home_model/report_profile_model.dart';
import 'package:room_eight/models/home_model/user_detail_model.dart';
import 'package:room_eight/models/profile_model/profile_model.dart';
import 'package:room_eight/repository/home_repository/home_repository.dart';
import 'package:swipable_stack/swipable_stack.dart';

part 'home_event.dart';
part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final HomeRepository homeRepository;

  HomeBloc(this.homeRepository)
    : super(
        HomeState(
          currentIndex: 0,
          pageController: PageController(),
          isPulseAnimating: false,
          carouselCurrentIndex: 0,
          hasSeenTutorial: true,
          swipableController: SwipableStackController(),
          reportController: TextEditingController(),
          
        ),
      ) {
    on<LoadHomePage>(_onLoadHomePage);
    on<HomePageChanged>(_onPageChanged);
    on<HomeResetToFirst>(_onResetToFirst);
    on<AcceptUser>(_onAcceptUser);
    on<RejectUser>(_onRejectUser);
    on<StartPulseAnimation>(_onStartPulseAnimation);
    on<StopPulseAnimation>(_onStopPulseAnimation);
    on<CarouselPageChanged>(_onCarouselPageChanged);
    on<CheckTutorialStatus>(_onCheckTutorialStatus);
    on<DismissTutorial>(_onDismissTutorial);
    on<InitializeSwipableController>(_onInitializeSwipableController);
    on<DisposeSwipableController>(_onDisposeSwipableController);
    on<GetUserProfileByID>(_onGetUserProfileByID);
    on<CurrentProfileIdChange>(_onCurrentProfileIdChange);
    on<BlockProfileSubmit>(_onBlockProfileSubmit);
    on<ReportProfileSubmit>(_onReportProfileSubmit);
    on<CurrentUserSelectionOptions>(_onCurrentUserSelectionOptions);

    add(const CheckTutorialStatus());
  }

  void _onLoadHomePage(LoadHomePage event, Emitter<HomeState> emit) async {
    try {
      emit(state.copyWith(loadHomePageData: true));

      GetAllUserDataModel result = await homeRepository.getAllUserData();

      if (result.status == true) {
        List<GetAllUserData> data = result.users ?? [];
        emit(state.copyWith(loadHomePageData: true, users: data));
      } else {
        Logger.lOG("Home Page All User Data Not loaded");
        emit(state.copyWith(loadHomePageData: false));
      }
    } catch (e) {
      Logger.lOG("Exception in loading home page data: $e");
      emit(state.copyWith(loadHomePageData: false));
    }
  }

  void _onPageChanged(HomePageChanged event, Emitter<HomeState> emit) {
    emit(state.copyWith(currentIndex: event.index));
  }

  void _onResetToFirst(HomeResetToFirst event, Emitter<HomeState> emit) {
    if (state.currentIndex != 0) {
      state.pageController.animateToPage(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      emit(state.copyWith(currentIndex: 0));
    }
  }

  void _onAcceptUser(AcceptUser event, Emitter<HomeState> emit) async {
    try {
      final Map<String, dynamic> data = {'like_profile_id': event.userId};
      CommonModel result = await homeRepository.likeUserProfile(data);

      if (result.status == true) {
        final updatedUsers = state.users
            .where((u) => u.id != event.userId)
            .toList();
        emit(state.copyWith(users: updatedUsers));
      } else {
        Logger.lOG("Like profile request failed: ${result.message}");
      }
    } catch (e) {
      Logger.lOG("Exception while liking profile: $e");
    }
  }

  void _onRejectUser(RejectUser event, Emitter<HomeState> emit) async {
    try {
      final Map<String, dynamic> data = {'dislike_profile_id': event.userId};
      CommonModel result = await homeRepository.dislikeUserProfile(data);

      if (result.status == true) {
        final updatedUsers = state.users
            .where((u) => u.id != event.userId)
            .toList();
        emit(state.copyWith(users: updatedUsers));
      } else {
        Logger.lOG("Dislike profile request failed: ${result.message}");
      }
    } catch (e) {
      Logger.lOG("Exception while disliking profile: $e");
    }
  }

  void _onStartPulseAnimation(
    StartPulseAnimation event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(isPulseAnimating: true));
  }

  void _onStopPulseAnimation(
    StopPulseAnimation event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(isPulseAnimating: false));
  }

  void _onCarouselPageChanged(
    CarouselPageChanged event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(carouselCurrentIndex: event.index));
  }

  Future<void> _onCheckTutorialStatus(
    CheckTutorialStatus event,
    Emitter<HomeState> emit,
  ) async {
    await Future.delayed(Duration.zero);
    final seen =
        Prefobj.preferences?.get(Prefkeys.SWIPE_TUTORIAL_SHOWN) ?? false;
    emit(state.copyWith(hasSeenTutorial: seen));
  }

  Future<void> _onDismissTutorial(
    DismissTutorial event,
    Emitter<HomeState> emit,
  ) async {
    await Prefobj.preferences?.put(Prefkeys.SWIPE_TUTORIAL_SHOWN, true);
    emit(state.copyWith(hasSeenTutorial: true));
  }

  void _onInitializeSwipableController(
    InitializeSwipableController event,
    Emitter<HomeState> emit,
  ) {
    final controller = SwipableStackController();
    emit(state.copyWith(swipableController: controller));
  }

  void _onDisposeSwipableController(
    DisposeSwipableController event,
    Emitter<HomeState> emit,
  ) {
    state.swipableController?.dispose();
    emit(state.copyWith(swipableController: null));
  }

  void _onGetUserProfileByID(
    GetUserProfileByID event,
    Emitter<HomeState> emit,
  ) async {
    emit(state.copyWith(isloadUserDetail: true));
    DetailedProfileModel result = await homeRepository.getUserProfileById(
      id: event.userId,
    );
    if (result.status == true) {
      final ProfileData? data = result.data;
      emit(state.copyWith(isloadUserDetail: false, userProfileDetail: data));
    } else {
      emit(state.copyWith(isloadUserDetail: true));
    }
  }

  void _onCurrentProfileIdChange(
    CurrentProfileIdChange event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(currentProfileId: event.index));
  }

  void _onBlockProfileSubmit(
    BlockProfileSubmit event,
    Emitter<HomeState> emit,
  ) async {
    final Map<String, dynamic> data = {"profile_id": event.userId};
    BlockProfileModel result = await homeRepository.blockProfile(data: data);

    if (result.status == true) {
    } else {
      Logger.lOG("User Profile is not blocked.");
    }
  }

  void _onReportProfileSubmit(
    ReportProfileSubmit event,
    Emitter<HomeState> emit,
  ) async {
    final Map<String, dynamic> data = {
      "profile_id": event.userId,
      "reason": state.reportController.text.trim(),
    };
    ReportProfileModel result = await homeRepository.reportProfile(data: data);
    // emit(state.copyWith(reportController: state.reportController));
    state.reportController.clear();
    if (result.status == true) {
    } else {
      Logger.lOG("User Profile is not reported.");
    }
  }
  void _onCurrentUserSelectionOptions(
    CurrentUserSelectionOptions event,
    Emitter<HomeState> emit,
  ) {
    final filteredHabits = state.habitsLifestyle
        .where((e) => event.habitsLifestyle.contains(e.id))
        .toList();

    final filteredLiving = state.livingStyle
        .where((e) => event.livingStyle.contains(e.id))
        .toList();

    final filteredInterests = state.interestsHobbies
        .where((e) => event.interestsHobbies.contains(e.id))
        .toList();

    final combinedSelections = [
      ...filteredHabits,
      ...filteredLiving,
      ...filteredInterests,
    ];

    emit(state.copyWith(userOptionsData: combinedSelections));
  }
}
