// ignore: depend_on_referenced_packages
import 'package:equatable/equatable.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/models/move_out_model/move_out_model.dart';
import 'package:room_eight/models/move_in_model/move_in_model.dart';
import 'package:room_eight/repository/like_repository/like_repository.dart';

part 'like_event.dart';
part 'like_state.dart';

class LikeBloc extends Bloc<LikeEvent, LikeState> {
  final PageController pageController = PageController();
  final LikeRepository likeRepository;
  LikeBloc(this.likeRepository) : super(LikeState()) {
    on<TabChangedEvent>(_onTabChangedEvent);
    on<LoadMoveOutData>(_onLoadMoveOutData);
    on<LoadMoveInData>(_onLoadMoveInData);
  }
  void _onTabChangedEvent(TabChangedEvent event, Emitter<LikeState> emit) {
    print('Tab switched to index: ${event.newIndex}');
    pageController.jumpToPage(event.newIndex);
    emit(state.copyWith(curentTebIndex: event.newIndex));
  }

  void _onLoadMoveOutData(
    LoadMoveOutData event,
    Emitter<LikeState> emit,
  ) async {
    emit(state.copyWith(isLoadingMoveOut: true));
    try {
      MoveOutModel moveOut = await likeRepository.getMoveOutData();

      if (moveOut.status == true) {
        final List<MoveOutUser> moveOutData = moveOut.data ?? [];

        print('=== MOVE OUT API RESPONSE DEBUG ===');
        print('Status: ${moveOut.status}');
        print('Message: ${moveOut.message}');
        print('Total users: ${moveOutData.length}');

        // Debug: Print profile images with more details
        for (int i = 0; i < moveOutData.length; i++) {
          var user = moveOutData[i];
          print('User $i:');
          print('  - ID: ${user.id}');
          print('  - Name: "${user.name}"');
          print('  - ProfileImage: "${user.profileImage}"');
          print(
            '  - ProfileImage isEmpty: ${user.profileImage?.isEmpty ?? true}',
          );
          print('  - ProfileImage length: ${user.profileImage?.length ?? 0}');
          print('  - About: "${user.about}"');
          print('  - Year: "${user.year}"');
          print('  - Status: ${user.status}');
        }
        print('===================================');

        emit(state.copyWith(moveOutData: moveOutData, isLoadingMoveOut: false));
      } else {
        emit(state.copyWith(isLoadingMoveOut: false));
      }
    } catch (e) {
      emit(state.copyWith(isLoadingMoveOut: false));
    }
  }

  void _onLoadMoveInData(LoadMoveInData event, Emitter<LikeState> emit) async {
    emit(state.copyWith(isLoadingMoveIn: true));
    try {
      MoveInModel moveIn = await likeRepository.getMoveInData();

      if (moveIn.status == true) {
        final List<MoveInUser> moveInData = moveIn.data ?? [];

        print('=== MOVE IN API RESPONSE DEBUG ===');
        print('Status: ${moveIn.status}');
        print('Message: ${moveIn.message}');
        print('Total users: ${moveInData.length}');

        // Debug: Print profile images with more details
        for (int i = 0; i < moveInData.length; i++) {
          var user = moveInData[i];
          print('User $i:');
          print('  - ID: ${user.id}');
          print('  - Name: "${user.name}"');
          print('  - ProfileImage: "${user.profileImage}"');
          print(
            '  - ProfileImage isEmpty: ${user.profileImage?.isEmpty ?? true}',
          );
          print('  - ProfileImage length: ${user.profileImage?.length ?? 0}');
          print('  - About: "${user.about}"');
          print('  - Year: "${user.year}"');
          print('  - Status: ${user.status}');
        }
        print('==================================');

        emit(state.copyWith(moveInData: moveInData, isLoadingMoveIn: false));
      } else {
        emit(state.copyWith(isLoadingMoveIn: false));
      }
    } catch (e) {
      emit(state.copyWith(isLoadingMoveIn: false));
    }
  }

  @override
  Future<void> close() {
    pageController.dispose();
    return super.close();
  }
}
