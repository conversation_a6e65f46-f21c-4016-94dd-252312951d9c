import 'package:room_eight/core/api_config/client/api_client.dart';
import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/models/common_model/common_model.dart';
import 'package:room_eight/models/move_out_model/move_out_model.dart';
import 'package:room_eight/models/move_in_model/move_in_model.dart';

class LikeRepository {
  final ApiClient apiClient;
  LikeRepository({required this.apiClient});

  Future<MoveOutModel> getMoveOutData() async {
    try {
      var response = await apiClient.request(
        RequestType.GET,
        ApiEndPoint.getMoveOutProfiles,
      );

      // Debug: Print raw API response
      print('=== RAW MOVE OUT API RESPONSE ===');
      print('Response: $response');
      if (response['data'] != null) {
        print('Data array length: ${response['data'].length}');
        for (int i = 0; i < response['data'].length && i < 3; i++) {
          print('Raw User $i: ${response['data'][i]}');
        }
      }
      print('================================');

      return MoveOutModel.fromJson(response);
    } catch (error) {
      print('Error in getMoveOutData: $error');
      rethrow;
    }
  }

  Future<MoveInModel> getMoveInData() async {
    try {
      var response = await apiClient.request(
        RequestType.GET,
        ApiEndPoint.getMoveInnProfiles,
      );

      // Debug: Print raw API response
      print('=== RAW MOVE IN API RESPONSE ===');
      print('Response: $response');
      if (response['data'] != null) {
        print('Data array length: ${response['data'].length}');
        for (int i = 0; i < response['data'].length && i < 3; i++) {
          print('Raw User $i: ${response['data'][i]}');
        }
      }
      print('===============================');

      return MoveInModel.fromJson(response);
    } catch (error) {
      print('Error in getMoveInData: $error');
      rethrow;
    }
  }

  Future<CommonModel> acceptLike({
    required int likeProfileId,
    required bool isAccept,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'email': likeProfileId,
        'password': isAccept,
      };
      var response = await apiClient.request(
        RequestType.POST,
        ApiEndPoint.acceptLikeUrl,
        data: data,
      );
      return CommonModel.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }
}
