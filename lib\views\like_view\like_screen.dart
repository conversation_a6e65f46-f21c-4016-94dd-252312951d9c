import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/viewmodels/like_bloc/like_bloc.dart';

class LikeScreen extends StatefulWidget {
  const LikeScreen({super.key});
  static Widget builder(BuildContext context) => const LikeScreen();

  @override
  State<LikeScreen> createState() => _LikeScreenState();
}

class _LikeScreenState extends State<LikeScreen> {
  @override
  void initState() {
    super.initState();
    // Load data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<LikeBloc>().add(LoadMoveOutData());
      context.read<LikeBloc>().add(LoadMoveInData());
    });
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: Theme.of(context).customColors.scaffoldColor,
        floatingActionButton: GestureDetector(
          onTap: () => NavigatorService.pushNamed(AppRoutes.chatscreen),
          child: Padding(
            padding: const EdgeInsets.only(bottom: 70.0),
            child: Container(
              margin: const EdgeInsets.only(right: 16.0),
              height: 56.h,
              width: 56.w,
              decoration: BoxDecoration(
                color: Theme.of(context).customColors.primaryColor,
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.shade300,
                    blurRadius: 5.r,
                    offset: Offset(1, 2),
                  ),
                ],
              ),
              child: Center(child: Icon(Icons.add)),
            ),
          ),
        ),
        body: BlocBuilder<LikeBloc, LikeState>(
          builder: (context, state) {
            if (state.isLoadData) {
              return Center(child: CircularProgressIndicator());
            }
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.r),
              child: Column(
                children: [
                  buildSizedBoxH(50.h),
                  _buildTabView(context),
                  Expanded(
                    child: PageView(
                      controller: context.read<LikeBloc>().pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        _buildMoveOutView(context),
                        _buildMoveInView(context),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTabView(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.fillColor,
        borderRadius: BorderRadius.circular(100.r),
      ),
      child: TabBar(
        onTap: (index) {
          context.read<LikeBloc>().add(TabChangedEvent(index));
          // Load data for the selected tab if not already loaded
          if (index == 0) {
            // Move Out tab
            final state = context.read<LikeBloc>().state;
            if (state.moveOutData.isEmpty && !state.isLoadingMoveOut) {
              context.read<LikeBloc>().add(LoadMoveOutData());
            }
          } else if (index == 1) {
            // Move In tab
            final state = context.read<LikeBloc>().state;
            if (state.moveInData.isEmpty && !state.isLoadingMoveIn) {
              context.read<LikeBloc>().add(LoadMoveInData());
            }
          }
        },
        indicatorSize: TabBarIndicatorSize.tab,
        labelStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
          fontSize: 16.sp,
          fontWeight: FontWeight.w500,
        ),
        labelColor: Theme.of(context).customColors.fillColor,
        dividerColor: Colors.transparent,
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        labelPadding: EdgeInsets.all(0.r),
        indicator: ShapeDecoration(
          color: Theme.of(context).customColors.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(100.r),
          ),
        ),
        tabs: [
          Tab(text: Lang.of(context).lbl_move_out),
          Tab(text: Lang.of(context).lbl_move_in),
        ],
      ),
    );
  }

  Widget _buildMoveOutView(BuildContext context) {
    return BlocBuilder<LikeBloc, LikeState>(
      builder: (context, state) {
        print('=== MOVE OUT VIEW BUILDER ===');
        print('Current tab index: ${state.curentTebIndex}');
        print('Move out data length: ${state.moveOutData.length}');
        print('Is loading move out: ${state.isLoadingMoveOut}');
        print('============================');

        // Show loading indicator if data is being loaded
        if (state.isLoadingMoveOut) {
          return const Center(child: CircularProgressIndicator());
        }

        // Show empty state if no data
        if (state.moveOutData.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.favorite_border, size: 64.r, color: Colors.grey),
                buildSizedBoxH(16.h),
                Text(
                  'No Move Out data available',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey,
                    fontSize: 16.sp,
                  ),
                ),
                buildSizedBoxH(20.h),
                ElevatedButton(
                  onPressed: () {
                    context.read<LikeBloc>().add(LoadMoveOutData());
                  },
                  child: const Text('Refresh'),
                ),
              ],
            ),
          );
        }

        // Show data list using the old UI design
        return RefreshIndicator(
          onRefresh: () async {
            context.read<LikeBloc>().add(LoadMoveOutData());
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Padding(
              padding: EdgeInsets.only(bottom: 80.w),
              child: Column(
                children: [
                  buildSizedBoxH(20.h),
                  ...state.moveOutData.map(
                    (user) => Column(
                      children: [
                        buildUserProfileCard(
                          context: context,
                          imageUrl: user.profileImage ?? '',
                          name: user.name?.isNotEmpty == true
                              ? user.name!
                              : 'Unknown User',
                          department: user.about ?? 'N/A',
                          onLike: () {
                            // Handle like action for move out user
                          },
                          onDislike: () {
                            // Handle dislike action for move out user
                          },
                          onFavorite: () {
                            // Handle favorite action for move out user
                          },
                        ),
                        buildSizedBoxH(20.h),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMoveInView(BuildContext context) {
    return BlocBuilder<LikeBloc, LikeState>(
      builder: (context, state) {
        print('=== MOVE IN VIEW BUILDER ===');
        print('Current tab index: ${state.curentTebIndex}');
        print('Move in data length: ${state.moveInData.length}');
        print('Is loading move in: ${state.isLoadingMoveIn}');
        for (int i = 0; i < state.moveInData.length && i < 3; i++) {
          print(
            'Move In User $i: ${state.moveInData[i].name} - Image: "${state.moveInData[i].profileImage}"',
          );
        }
        print('===========================');

        // Show loading indicator if data is being loaded
        if (state.isLoadingMoveIn) {
          return const Center(child: CircularProgressIndicator());
        }

        // Show empty state if no data
        if (state.moveInData.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.favorite, size: 64.r, color: Colors.grey),
                buildSizedBoxH(16.h),
                Text(
                  'No Move In data available',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey,
                    fontSize: 16.sp,
                  ),
                ),
                buildSizedBoxH(20.h),
                ElevatedButton(
                  onPressed: () {
                    context.read<LikeBloc>().add(LoadMoveInData());
                  },
                  child: const Text('Refresh'),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            context.read<LikeBloc>().add(LoadMoveInData());
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Padding(
              padding: EdgeInsets.only(bottom: 80.w),
              child: Column(
                children: [
                  buildSizedBoxH(20.h),
                  ...state.moveInData.map(
                    (user) => Column(
                      children: [
                        buildUserProfileCard(
                          context: context,
                          imageUrl: user.profileImage ?? '',
                          name: user.name?.isNotEmpty == true
                              ? user.name!
                              : 'Unknown User',
                          department: user.about ?? 'N/A',
                          onLike: () {
                            // Handle like action for move in user
                          },
                          onDislike: () {
                            // Handle dislike action for move in user
                          },
                          onFavorite: () {
                            // Handle favorite action for move in user
                          },
                        ),
                        buildSizedBoxH(20.h),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget buildUserProfileCard({
    required BuildContext context,
    required String imageUrl,
    required String name,
    required String department,
    required VoidCallback onLike,
    required VoidCallback onDislike,
    required VoidCallback onFavorite,
  }) {
    // Enhanced debug print to check image URL
    print('=== IMAGE DEBUG ===');
    print('User Name: "$name"');
    print('Raw Image URL: "$imageUrl"');
    print('Image URL isEmpty: ${imageUrl.isEmpty}');
    print('Image URL length: ${imageUrl.length}');
    print('Image URL trimmed: "${imageUrl.trim()}"');

    // Clean and validate the image URL
    final String cleanImageUrl = imageUrl.trim();
    final bool hasValidImage =
        cleanImageUrl.isNotEmpty &&
        cleanImageUrl != 'null' &&
        cleanImageUrl != '""' &&
        cleanImageUrl != 'null';

    final String fullImageUrl = hasValidImage
        ? '${ApiEndPoint.getImageUrl}$cleanImageUrl'
        : '';

    print('Clean Image URL: "$cleanImageUrl"');
    print('Has Valid Image: $hasValidImage');
    print('Base URL: "${ApiEndPoint.getImageUrl}"');
    print('Full Image URL: "$fullImageUrl"');
    print('==================');

    return Container(
      padding: EdgeInsets.all(8.r),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.fillColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(color: Colors.black12, blurRadius: 8, offset: Offset(0, 4)),
        ],
      ),
      child: Column(
        children: [
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(12.r),
                child: hasValidImage
                    ? Image.network(
                        fullImageUrl,
                        height: 180.h,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          print('Image load error: $error');
                          return _buildPlaceholderImage(userName: name);
                        },
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Container(
                            height: 180.h,
                            width: double.infinity,
                            color: Colors.grey[200],
                            child: Center(
                              child: CircularProgressIndicator(
                                value:
                                    loadingProgress.expectedTotalBytes != null
                                    ? loadingProgress.cumulativeBytesLoaded /
                                          loadingProgress.expectedTotalBytes!
                                    : null,
                              ),
                            ),
                          );
                        },
                      )
                    : _buildPlaceholderImage(userName: name),
              ),
              Positioned(
                top: 10.h,
                right: 10.w,
                child: InkWell(
                  onTap: onFavorite,
                  child: CircleAvatar(
                    radius: 20.r,
                    backgroundColor: Colors.white70,
                    child: CustomImageView(
                      imagePath: Assets.images.svgs.icons.icLike.path,
                      margin: EdgeInsets.all(10.r),
                    ),
                  ),
                ),
              ),
            ],
          ),
          buildSizedBoxH(12.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                name,
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16.sp),
              ),
              Row(
                children: [
                  GestureDetector(
                    onTap: onDislike,
                    child: Container(
                      height: 30.h,
                      width: 30.w,
                      decoration: BoxDecoration(
                        color: Theme.of(context).customColors.fillColor,
                        borderRadius: BorderRadius.circular(100.r),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.shade300,
                            blurRadius: 5.r,
                            offset: Offset(1, 2),
                          ),
                        ],
                      ),
                      child: CustomImageView(
                        imagePath: Assets.images.svgs.icons.icClose.path,
                        margin: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 8.h,
                        ),
                      ),
                    ),
                  ),
                  buildSizedboxW(5.w),
                  GestureDetector(
                    onTap: onLike,
                    child: Container(
                      height: 30.h,
                      width: 30.w,
                      decoration: BoxDecoration(
                        color: Theme.of(context).customColors.fillColor,
                        borderRadius: BorderRadius.circular(100.r),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.shade300,
                            blurRadius: 5.r,
                            offset: Offset(1, 2),
                          ),
                        ],
                      ),
                      child: CustomImageView(
                        imagePath: Assets.images.svgs.icons.icWirite.path,
                        margin: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 8.h,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          Row(
            children: [
              Text(
                department,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontSize: 14.sp,
                  color: Theme.of(context).customColors.darkGreytextcolor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceholderImage({String? userName}) {
    // Generate initials from user name
    String initials = _generateInitials(userName);

    return Container(
      height: 180.h,
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            (Theme.of(context).customColors.primaryColor ?? Colors.blue)
                .withValues(alpha: 0.3),
            (Theme.of(context).customColors.primaryColor ?? Colors.blue)
                .withValues(alpha: 0.6),
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (initials.isNotEmpty)
            Container(
              width: 80.r,
              height: 80.r,
              decoration: BoxDecoration(
                color:
                    Theme.of(context).customColors.primaryColor ?? Colors.blue,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  initials,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 32.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            )
          else
            Icon(Icons.person, size: 60.r, color: Colors.grey[600]),
          buildSizedBoxH(8.h),
          Text(
            initials.isNotEmpty ? 'No Image Available' : 'No Image',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  String _generateInitials(String? name) {
    if (name == null || name.trim().isEmpty) return '';

    final words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0].substring(0, 1).toUpperCase();
    } else {
      return '${words[0].substring(0, 1)}${words[1].substring(0, 1)}'
          .toUpperCase();
    }
  }
}
